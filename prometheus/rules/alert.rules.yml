groups:
- name: SalmateHealthAlerts
  rules:

  # 1. Host/Instance/Container/Service Down
  - alert: InstanceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Instance {{ $labels.instance }} of job {{ $labels.job }} is down"
      description: "Instance {{ $labels.instance }} (job: {{ $labels.job }}) in environment {{ $labels.environment }} is unreachable for more than 1 minute."

  # 2. High CPU Usage (>90% for 5m)
  - alert: HighCPUUsage
    expr: 100 - (avg by (environment, instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage detected"
      description: "Instance {{ $labels.instance }} in {{ $labels.environment }} is above 90% CPU usage for 5m."

  # 3. High Memory Usage (>90% for 5m)
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 90
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High Memory usage detected"
      description: "Instance {{ $labels.instance }} in {{ $labels.environment }} is above 90% memory usage for 5m."

  # 4. High Disk Usage (>90% for 5m, on /)
  - alert: HighDiskUsage
    expr: 100 - (node_filesystem_avail_bytes{mountpoint="/"} * 100 / node_filesystem_size_bytes{mountpoint="/"}) > 90
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High Disk usage detected"
      description: "Instance {{ $labels.instance }} in {{ $labels.environment }} mount / is above 90% usage for 5m."

  # 5. Certificate Expiry (<7 days)
  - alert: CertificateExpirySoon
    expr: (probe_ssl_earliest_cert_expiry - time()) / 86400 < 7
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: "SSL certificate expiring soon"
      description: "Certificate for {{ $labels.instance }} in {{ $labels.environment }} expires in less than 7 days."

  # 6. Website Down (HTTP status != 200)
  - alert: WebsiteDown
    expr: probe_http_status_code != 200
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "Website down or not returning 200"
      description: "Website {{ $labels.instance }} in {{ $labels.environment }} is not returning HTTP 200 for 2 minutes."

  # 7. Slow HTTP Response Time (P99 > 1s for 5m)
  - alert: HighHTTPResponseTimeP99
    expr: histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, environment, instance)) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High HTTP response time (P99)"
      description: "P99 response time for {{ $labels.instance }} in {{ $labels.environment }} exceeded 1s for 5m."