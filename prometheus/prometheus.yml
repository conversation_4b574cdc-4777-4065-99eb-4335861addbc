global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: "salmate-central-monitor"

# Rule files
rule_files:
  - "rules/*.yml"

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

# Scrape configurations
scrape_configs:
  # Self monitoring
  # - job_name: "prometheus"
  #   scrape_interval: 5s
  #   static_configs:
  #     - targets: ["localhost:9090"]

  # Node exporter for the central monitoring server
  # - job_name: "node-exporter"
  #   static_configs:
  #     - targets: ["node-exporter:9100"]
  #       labels:
  #         instance: "central-monitor"

  # cAdvisor for container metrics on the central monitoring server
  # - job_name: "cadvisor"
  #   static_configs:
  #     - targets: ["cadvisor:8080"]

  # Federated Prometheus
  - job_name: "federate"
    scrape_interval: 15s
    honor_labels: true
    metrics_path: "/federate"
    params:
      "match[]":
        - '{__name__=~".+"}'
    static_configs:
      - targets:
          - "prom.salmate-staging.aibrainlab.co"
        labels:
          environment: "staging"
      - targets:
          - "prom.tpb.aibrainlab.co"
        labels:
          environment: "tpb"
      - targets:
          - "prom.viriyah.aibrainlab.co"
        labels:
          environment: "viriyah"
      - targets:
          - "prom.sila-dev.aibrainlab.co"
        labels:
          environment: "sila"

  - job_name: "blackbox"
    metrics_path: /probe
    params:
      module: [http_2xx, icmp] # HTTP check for 2xx response
    static_configs:
      - targets:
          - https://www.salmate-staging.aibrainlab.co
        labels:
          environment: "staging"
      - targets:
          - https://www.tpb.aibrainlab.co
        labels:
          environment: "tpb"
      - targets:
          - https://www.viriyah.aibrainlab.co
        labels:
          environment: "viriyah"
      - targets:
          - https://www.sila-dev.aibrainlab.co
        labels:
          environment: "sila"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115
