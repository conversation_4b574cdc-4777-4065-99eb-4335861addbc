[server]
# The public facing domain name used to access grafana from a browser
domain = ${DOMAIN_NAME}
# The full public facing url you use in browser, used for redirects and emails
root_url = https://${DOMAIN_NAME}/grafana
# Serve grafana from subpath specified in `root_url` setting
serve_from_sub_path = true

[security]
# Security settings for HTTPS
cookie_secure = true
# Allow embedding Grafana in iframes (required for some dashboards)
allow_embedding = true
# Disable initial admin creation if already configured
disable_initial_admin_creation = false

[users]
# User management settings
disable_signup_menu = true
auto_assign_org = true
auto_assign_org_role = Editor

[auth]
# Authentication settings
disable_login_form = false
# Disable anonymous access
aanon_enabled = false

[users]
allow_sign_up = false
auto_assign_org = true
auto_assign_org_role = Editor
