{"__inputs": [{"name": "Prometheus", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "12.0.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 8, "panels": [], "title": "CPU", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 1}, "id": 15, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(rate(container_cpu_usage_seconds_total{environment=\"$environment\",name=~\"$container\",name=~\".+\"}[5m])) by (name) *100", "hide": false, "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "CPU Usage", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 11, "panels": [], "title": "Memory", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 9, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(container_memory_rss{environment=\"$environment\",name=~\"$container\",name=~\".+\"}) by (name)", "hide": false, "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 14, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(container_memory_cache{environment=\"$environment\",name=~\"$container\",name=~\".+\"}) by (name)", "hide": false, "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Memory Cached", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 2, "panels": [], "title": "Network", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 4, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(rate(container_network_receive_bytes_total{environment=\"$environment\",name=~\"$container\",name=~\".+\"}[5m])) by (name)", "hide": false, "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Received Network Traffic", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 6, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(rate(container_network_transmit_bytes_total{environment=\"$environment\",name=~\"$container\",name=~\".+\"}[5m])) by (name)", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Sent Network Traffic", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 19, "panels": [], "title": "Misc", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "id"}, "properties": [{"id": "custom.width", "value": 260}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Running"}, "properties": [{"id": "unit", "value": "d"}, {"id": "decimals", "value": 1}, {"id": "custom.cellOptions", "value": {"type": "color-text"}}, {"id": "color", "value": {"fixedColor": "dark-green", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 27}, "id": 17, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(time() - container_start_time_seconds{environment=\"$environment\",name=~\"$container\",name=~\".+\"})/86400", "format": "table", "instant": true, "interval": "", "legendFormat": "{{name}}", "refId": "A"}], "title": "Containers Info", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["container_label_com_docker_compose_project", "container_label_com_docker_compose_project_working_dir", "image", "instance", "name", "Value", "container_label_com_docker_compose_service"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Value": "Running", "container_label_com_docker_compose_project": "Label", "container_label_com_docker_compose_project_working_dir": "Working dir", "container_label_com_docker_compose_service": "Service", "image": "Registry Image", "instance": "Instance", "name": "Name"}}}], "type": "table"}], "refresh": "", "schemaVersion": 41, "tags": ["cadvisor", "prometheus"], "templating": {"list": [{"allowCustomValue": false, "current": {}, "definition": "label_values(environment)", "includeAll": false, "label": "Environment", "name": "environment", "options": [], "query": {"qryType": 1, "query": "label_values(environment)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"allValue": ".*", "current": {}, "datasource": {"type": "prometheus"}, "definition": "label_values({__name__=~\"container.*\"},name)", "includeAll": true, "label": "Container", "name": "container", "options": [], "query": {"qryType": 1, "query": "label_values({__name__=~\"container.*\"},name)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "View Containers", "uid": "_containers_", "version": 1, "weekStart": ""}