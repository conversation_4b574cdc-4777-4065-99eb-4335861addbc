{"__inputs": [{"name": "Prometheus", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "12.0.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 34, "panels": [], "title": "General Counters, CPU, Memory and File Descriptor Stats", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 2, "y": 1}, "id": 10, "maxDataPoints": 1, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(increase(pg_stat_database_tup_fetched{environment=\"$environment\", datname=\"salmate\"}[$__interval]))", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A", "step": 4}], "title": "<PERSON><PERSON> fetched during the period", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 6, "y": 1}, "id": 11, "maxDataPoints": 1, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(pg_stat_database_tup_inserted{environment=\"$environment\", datname=\"salmate\"}[$__interval]))", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 4}], "title": "Tuple inserted during the period", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 10, "y": 1}, "id": 12, "maxDataPoints": 1, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(pg_stat_database_tup_updated{environment=\"$environment\", datname=\"salmate\"}[$__interval]))", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 4}], "title": "<PERSON><PERSON> updated during the period", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 14, "y": 1}, "id": 89, "maxDataPoints": 1, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(pg_stat_database_tup_deleted{environment=\"$environment\", datname=\"salmate\"}[$__interval]))", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 4}], "title": "Tu<PERSON> deleted during the period", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 18, "y": 1}, "id": 90, "maxDataPoints": 1, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(pg_stat_database_tup_returned{environment=\"$environment\", datname=\"salmate\"}[$__interval]))", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 4}], "title": "<PERSON><PERSON> returned during the period", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "Average user and system CPU time spent in seconds.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 4}, "id": 22, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(container_cpu_usage_seconds_total{environment=\"$environment\", name=~\"postgres-db.+\"}[4m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "CPU", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Virtual and Resident memory size in bytes, averages over 5 min interval", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 4}, "id": 24, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "container_memory_working_set_bytes{environment=\"$environment\", name=~\"postgres-db.+\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Working set: {{name}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "container_memory_cache{environment=\"$environment\", name=~\"postgres-db.+\"}", "interval": "", "legendFormat": "Cache: {{name}}", "range": true, "refId": "D"}], "title": "Memory", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 30, "panels": [], "title": "Database Stats", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsNull", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 12}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "hoverProximity": -6, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum by (state) (pg_stat_activity_count{environment=\"$environment\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "__auto", "range": true, "refId": "A", "step": 2}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "pg_stat_database_numbackends{environment=\"$environment\"}", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "__auto", "range": true, "refId": "B", "step": 2}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "max(pg_settings_max_connections{environment=\"$environment\"})", "hide": false, "instant": false, "legendFormat": "max_connections", "range": true, "refId": "C"}], "title": "PG Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 12}, "id": 60, "interval": "30s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_xact_commit{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "commits", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_xact_rollback{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "rollbacks", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(pg_stat_database_xact_rollback{environment=\"$environment\"}[2m]) + irate(pg_stat_database_xact_commit{environment=\"$environment\"}[2m]))", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "total", "range": true, "refId": "C"}], "title": "Transactions", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 12}, "id": 76, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "max(pg_stat_activity_max_tx_duration{environment=\"$environment\"})", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "pg_stat_activity_max_tx_duration", "refId": "A", "step": 2}], "title": "Longest Transaction", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 19}, "id": 72, "interval": "30s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_returned{environment=\"$environment\"}[2m]))", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "tup_returned (seq scan)", "range": true, "refId": "A", "step": 2}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(pg_stat_database_tup_fetched{environment=\"$environment\"}[2m]))", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "tup_fetched (idx access)", "range": true, "refId": "B", "step": 2}], "title": "Read tuple activity per second", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 19}, "id": 74, "interval": "30", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_tup_inserted{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "tup_inserted", "metric": "pg_stat_database_tup", "range": true, "refId": "C", "step": 2}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_tup_updated{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "tup_updated", "metric": "pg_stat_database_tup", "range": true, "refId": "D", "step": 2}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_tup_deleted{environment=\"$environment\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "tup_deleted", "metric": "pg_stat_database_tup", "range": true, "refId": "E", "step": 2}], "title": "Changed tuples activity per second", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "red"}, {"color": "green", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 19}, "id": 62, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_stat_database_blks_hit{environment=\"$environment\", datname=\"salmate\"} / (pg_stat_database_blks_read{environment=\"$environment\", datname=\"salmate\"} + pg_stat_database_blks_hit{environment=\"$environment\", datname=\"salmate\"})", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "Hit rate", "range": false, "refId": "A"}], "title": "<PERSON><PERSON> Hit Rate", "type": "stat"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 26}, "id": 87, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "max(pg_database_size_bytes{environment=\"$environment\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Database size", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 26}, "id": 88, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "pg_wal_size_bytes{environment=\"$environment\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "WAL size", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 26}, "id": 80, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "pg_replication_lag_seconds{environment=\"$environment\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "metric": "pg_replication_lag", "range": true, "refId": "A", "step": 2}], "title": "Replication Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["accessexclusivelock"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 33}, "id": 3, "interval": "30s", "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_locks_count{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{mode}}", "range": true, "refId": "A", "step": 2}], "title": "Lock tables", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 33}, "id": 64, "interval": "30s", "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_backend_total{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "buffers_backend", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_alloc_total{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "buffers_alloc", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_backend_fsync_total{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "backend_fsync", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_checkpoint_total{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "buffers_checkpoint", "range": true, "refId": "D"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_clean_total{environment=\"$environment\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "buffers_clean", "range": true, "refId": "E"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_backend{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "buffers_backend", "range": true, "refId": "F"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_alloc{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "buffers_alloc", "range": true, "refId": "G"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_backend_fsync{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "backend_fsync", "range": true, "refId": "H"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_checkpoint{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "buffers_checkpoint", "range": true, "refId": "I"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_buffers_clean{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "buffers_clean", "range": true, "refId": "J"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_maxwritten_clean_total{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "maxwritten_clean", "range": true, "refId": "K"}], "title": "<PERSON><PERSON><PERSON> (bgwriter)", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Number of open file descriptors", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 33}, "id": 26, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "process_open_fds{environment=\"$environment\", job=~\"vectordb|tool\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{job}}", "range": true, "refId": "A"}], "title": "Open File Descriptors", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 40}, "id": 70, "interval": "30s", "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_checkpoint_write_time_total{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "write_time - Total amount of time that has been spent in the portion of checkpoint processing where files are written to disk.", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_bgwriter_checkpoint_sync_time_total{environment=\"$environment\"}[2m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "sync_time - Total amount of time that has been spent in the portion of checkpoint processing where files are synchronized to disk.", "range": true, "refId": "D"}], "title": "Checkpoint Stats", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Total amount of data written to temporary files by queries in this database. All temporary files are counted, regardless of why the temporary file was created, and regardless of the log_temp_files setting.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 40}, "id": 68, "interval": "30s", "options": {"legend": {"calcs": ["mean", "lastNotNull", "sum"], "displayMode": "list", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_temp_bytes{environment=\"$environment\", datname=\"salmate\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{datname}}", "range": true, "refId": "A"}], "title": "Temp File (Bytes)", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 40}, "id": 66, "interval": "30s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_conflicts{environment=\"$environment\", datname=\"salmate\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "conflicts", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(pg_stat_database_deadlocks{environment=\"$environment\", datname=\"salmate\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "deadlocks", "range": true, "refId": "A"}], "title": "Conflicts/Deadlocks", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 47}, "id": 32, "panels": [], "title": "Settings", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 48}, "id": 40, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_shared_buffers_bytes{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Shared Buffers", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 48}, "id": 42, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_effective_cache_size_bytes{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Effective Cache", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 48}, "id": 44, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_maintenance_work_mem_bytes{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Maintenance Work Mem", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 48}, "id": 46, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_work_mem_bytes{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Work Mem", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 48}, "id": 50, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_random_page_cost{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Random Page Cost", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 48}, "id": 52, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_seq_page_cost{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "<PERSON><PERSON>", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 18, "y": 48}, "id": 54, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_max_worker_processes{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "Max Worker Processes", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 48}, "id": 56, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "pg_settings_max_parallel_workers{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "range": false, "refId": "A"}], "title": "<PERSON>", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 51}, "id": 36, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "name", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "pg_static{environment=\"$environment\"}", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{short_version}}", "refId": "A"}], "title": "Version", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 54}, "id": 81, "panels": [], "title": "Table Stats", "type": "row"}, {"datasource": {"type": "prometheus"}, "description": "Number of buffer hits in this table", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 55}, "id": 82, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "topk(5, max by (relname)(rate(pg_statio_user_tables_heap_blocks_hit{environment=\"$environment\"}[$__rate_interval])))", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Buffer hits", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Number of disk blocks read from this table", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 55}, "id": 83, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "topk(5, max by (relname)(rate(pg_statio_user_tables_heap_blocks_read{environment=\"$environment\"}[$__rate_interval])))", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Disk block reads", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Number of buffer hits in all indexes on this table", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 63}, "id": 84, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "topk(5, max by (relname)(rate(pg_statio_user_tables_idx_blocks_hit{environment=\"$environment\"}[$__rate_interval])))", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Index buffer hits", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "description": "Number of disk blocks read from all indexes on this table", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 63}, "id": 85, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "topk(5, max by (relname)(rate(pg_statio_user_tables_idx_blocks_read{environment=\"$environment\"}[$__rate_interval])))", "instant": false, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Disk index read ", "type": "timeseries"}], "refresh": "", "schemaVersion": 41, "tags": ["postgresql", "database"], "templating": {"list": [{"allowCustomValue": false, "current": {}, "definition": "label_values(environment)", "label": "Environment", "name": "environment", "options": [], "query": {"qryType": 1, "query": "label_values(environment)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "View PostgreSQL", "uid": "postgresql-database", "version": 20, "weekStart": ""}