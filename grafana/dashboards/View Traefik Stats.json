{"__inputs": [{"name": "Prometheus", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "12.0.0"}, {"type": "panel", "id": "piechart", "name": "Pie chart", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "panels": [], "title": "Service Stats", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "decimals": 0, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 2, "maxDataPoints": 3, "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true, "values": ["value", "percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "traefik_service_requests_total{environment=\"$environment\", service=\"$service\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "range": true, "refId": "A"}], "title": "$service return code", "type": "piechart"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 4, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(traefik_service_request_duration_seconds_sum{environment=\"$environment\", service=\"$service\"}) / sum(traefik_service_requests_total{environment=\"$environment\", service=\"$service\"}) * 1000", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "range": true, "refId": "A"}], "title": "$service response time", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"alertThreshold": true, "legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(traefik_service_requests_total{environment=\"$environment\", service=\"$service\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Total requests $service", "range": true, "refId": "A"}], "title": "Total requests over 5min $service", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 12, "panels": [], "title": "Global Stats", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "id": 5, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traefik_entrypoint_requests_total{environment=\"$environment\", entrypoint=~\"$entrypoint\",code=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{method}} : {{code}}", "range": true, "refId": "A"}], "title": "Status code 200 over 5min", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "id": 6, "options": {"alertThreshold": true, "legend": {"calcs": ["lastNotNull", "max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "rate(traefik_entrypoint_requests_total{environment=\"$environment\", entrypoint=~\"$entrypoint\",code!=\"200\"}[5m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ method }} : {{code}}", "range": true, "refId": "A"}], "title": "Others status code over 5min", "type": "timeseries"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "decimals": 0, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 23}, "id": 7, "maxDataPoints": 3, "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(traefik_service_requests_total{environment=\"$environment\"}[5m])) by (service) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ service }}", "range": true, "refId": "A"}], "title": "Requests by service", "type": "piechart"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "decimals": 0, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 23}, "id": 8, "maxDataPoints": 3, "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "showLegend": true, "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.0", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(traefik_entrypoint_requests_total{environment=\"$environment\", entrypoint =~ \"$entrypoint\"}[5m])) by (entrypoint) ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ entrypoint }}", "range": true, "refId": "A"}], "title": "Requests by protocol", "type": "piechart"}], "schemaVersion": 41, "tags": ["traefik", "prometheus"], "templating": {"list": [{"allowCustomValue": false, "current": {}, "definition": "label_values(environment)", "label": "Environment", "name": "environment", "options": [], "query": {"qryType": 1, "query": "label_values(environment)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {}, "datasource": {"type": "prometheus"}, "definition": "label_values(service)", "includeAll": false, "label": "Service", "name": "service", "options": [], "query": {"query": "label_values(service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {}, "datasource": {"type": "prometheus"}, "definition": "", "includeAll": true, "label": "Entrypoint", "multi": true, "name": "entrypoint", "options": [], "query": {"query": "label_values(entrypoint)", "refId": "Prometheus-entrypoint-Variable-Query"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "View Traefik Stats", "uid": "_traefik_", "version": 2, "weekStart": ""}