[server]
# Protocol (http, https, h2, socket)
protocol = http

# The http port to use
http_port = 3000

# The public facing domain name used to access grafana from a browser
domain = ${DOMAIN_NAME}

# The full public facing url you use in browser, used for redirects and emails
# If you use reverse proxy and sub path specify full url (with sub path)
root_url = https://${DOMAIN_NAME}/grafana/

# Serve <PERSON> from subpath specified in `root_url` setting. By default it is set to `false` for compatibility reasons.
serve_from_sub_path = true

# Enable this to ensure proper routing with Traefik
enable_x_forwarded_headers = true

# Log web requests
router_logging = true

[security]
# set to true if you host <PERSON><PERSON> behind HTTPS
disable_initial_admin_creation = false
cookie_secure = true
cookie_samesite = none
allow_embedding = true

# Set to true if you use a reverse proxy with a subpath
# This tells <PERSON><PERSON> to trust the X-Forwarded-* headers
# that the reverse proxy sets
enable_x_forwarded_headers = true

# Set to true if you use a reverse proxy with a subpath
# This tells <PERSON><PERSON> to trust the X-Real-IP header
# that the reverse proxy sets
trust_x_forwarded_headers = true

[auth.anonymous]
# enable anonymous access
enabled = false

# specify organization name that should be used for unauthenticated users
org_name = Main Org.

# specify role for unauthenticated users
org_role = Viewer

[auth.basic]
enabled = true

[session]
cookie_secure = true
cookie_samesite = none

[log]
# Either "console", "file", "syslog". Default is console and file
# Use space to separate multiple modes, e.g., "console file"
mode = console file

# Either "debug", "info", "warn", "error", "critical", default is "info"
level = info

# For "console" logger only. Valid values are "console", "console_ansi". Default is "console"
format = console

# Enable logging of source code location (file and line number) of all log messages
disable_src = false

[paths]
# Path to where grafana can store temp files, sessions, and the sqlite3 db (if that is used)
data = /var/lib/grafana

# Directory where grafana can store logs
temp_data_lifetime = 24h

# Directory where grafana will automatically scan and look for plugins
plugins = /var/lib/grafana/plugins

# Folder that contains provisioning config files that grafana will apply on startup and while running.
provisioning = /etc/grafana/provisioning

[users]
# disable user signup / registration
auto_assign_org = true
auto_assign_org_role = Viewer
