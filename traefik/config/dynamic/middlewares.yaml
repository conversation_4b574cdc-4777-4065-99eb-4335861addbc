http:
  middlewares:
    # Middleware to redirect HTTP to HTTPS
    redirect-to-https:
      redirectScheme:
        scheme: https
        permanent: true

    # Global secure headers middleware
    secure:
      headers:
        # Security headers
        sslRedirect: true
        stsSeconds: 31536000
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        referrerPolicy: "same-origin"
        customFrameOptionsValue: "SAMEORIGIN"

        # Forward the X-Forwarded-* headers
        customRequestHeaders:
          X-Forwarded-Proto: "https"

        # Content Security Policy
        contentSecurityPolicy: |
          default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:;
          img-src 'self' data: blob: https: http:;
          script-src 'self' 'unsafe-inline' 'unsafe-eval';
          style-src 'self' 'unsafe-inline';
          font-src 'self' data:;
          connect-src 'self' wss: https:;
          frame-ancestors 'self';
          object-src 'none';
          base-uri 'self';
          form-action 'self';
          frame-src 'self';
          media-src 'self';

    # Middleware to strip /grafana prefix
    strip-prefix:
      stripPrefix:
        prefixes:
          - "/grafana"
        forceSlash: false

  # Router to catch all HTTP requests and redirect to HTTPS, except for ACME challenges
  routers:
    http-catchall:
      entryPoints:
        - web
      rule: "HostRegexp(`{any:.*}`) && !PathPrefix(`/.well-known/acme-challenge/`)"
      middlewares:
        - redirect-to-https
      service: noop@internal
