# Cleaned up routes.yaml by removing commented sslRedirect and ensuring consistent middleware usage
http:
  middlewares:
    secureHeaders:
      headers:
        frameDeny: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000

    traefik-auth:
      basicAuth:
        users:
          - "admin:$apr1$9tO9ZQW3$vAE8mTfBuezZlDGSQMzQJ1"

    redirect-to-grafana:
      redirectRegex:
        regex: "^https?://([^/]+)/?$"
        replacement: "https://$1/grafana"
        permanent: true

    strip-prefix-prometheus:
      stripPrefix:
        prefixes:
          - "/prometheus"

    strip-prefix-grafana:
      stripPrefix:
        prefixes:
          - "/grafana"
        forceSlash: false

    strip-prefix-traefik:
      stripPrefix:
        prefixes:
          - "/traefik"
          - "/dashboard"

    strip-prefix-alertmanager:
      stripPrefix:
        prefixes:
          - "/alertmanager"

    grafana-headers:
      headers:
        customResponseHeaders:
          X-Frame-Options: "SAMEORIGIN"
          X-Content-Type-Options: "nosniff"
          X-XSS-Protection: "1; mode=block"
          Referrer-Policy: "strict-origin-when-cross-origin"
          Content-Security-Policy: "default-src 'self' 'unsafe-inline' 'unsafe-eval' data:;"
        sslRedirect: true
        stsSeconds: 31536000
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true

  routers:
    dashboard:
      rule: "Host(`${DOMAIN_NAME}`) && (PathPrefix(`/api`) || PathPrefix(`/dashboard`))"
      service: api@internal
      entryPoints:
        - websecure
      middlewares:
        - traefik-auth
        - strip-prefix-traefik
      tls:
        certResolver: letsencrypt

    root:
      rule: "Host(`${DOMAIN_NAME}`) && Path(`/`)"
      service: noop@internal
      entryPoints:
        - websecure
      middlewares:
        - redirect-to-grafana
      tls:
        certResolver: letsencrypt

    prometheus:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/prometheus`)"
      service: prometheus@docker
      entryPoints:
        - websecure
      middlewares:
        - strip-prefix-prometheus
      tls:
        certResolver: letsencrypt

    grafana-redirect:
      rule: "Host(`${DOMAIN_NAME}`) && Path(`/`)"
      service: noop@internal
      entryPoints:
        - websecure
      middlewares:
        - redirect-to-grafana
      tls:
        certResolver: letsencrypt

    grafana:
      rule: "Host(`${DOMAIN_NAME}`) && (PathPrefix(`/grafana`) || PathPrefix(`/grafana/`))"
      service: grafana@docker
      entryPoints:
        - websecure
      middlewares:
        - strip-prefix-grafana
        - secureHeaders
      tls:
        certResolver: letsencrypt

    alertmanager:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/alertmanager`)"
      service: alertmanager@docker
      entryPoints:
        - websecure
      middlewares:
        - strip-prefix-alertmanager
      tls:
        certResolver: letsencrypt
