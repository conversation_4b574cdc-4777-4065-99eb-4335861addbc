# Cleaned up routes.yaml by removing commented sslRedirect and ensuring consistent middleware usage
http:
  middlewares:
    secureHeaders:
      headers:
        frameDeny: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000

    traefik-auth:
      basicAuth:
        users:
          - "admin:$apr1$9tO9ZQW3$vAE8mTfBuezZlDGSQMzQJ1"

    redirect-from-grafana:
      redirectRegex:
        regex: "^https?://([^/]+)/grafana/?(.*)$"
        replacement: "https://$1/$2"
        permanent: true

    strip-prefix-prometheus:
      stripPrefix:
        prefixes:
          - "/prometheus"

    strip-prefix-traefik:
      stripPrefix:
        prefixes:
          - "/traefik"
        forceSlash: false

    strip-prefix-alertmanager:
      stripPrefix:
        prefixes:
          - "/alertmanager"

    grafana-headers:
      headers:
        customResponseHeaders:
          X-Frame-Options: "SAMEORIGIN"
          X-Content-Type-Options: "nosniff"
          X-XSS-Protection: "1; mode=block"
          Referrer-Policy: "strict-origin-when-cross-origin"
          Content-Security-Policy: "default-src 'self' 'unsafe-inline' 'unsafe-eval' data:;"
        sslRedirect: true
        stsSeconds: 31536000
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true

  routers:
    # HTTP router for Traefik dashboard - redirects to HTTPS
    traefik-dashboard-http:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/traefik`)"
      service: noop@internal
      entryPoints:
        - web
      middlewares:
        - redirect-to-https
      priority: 10

    # HTTPS router for Traefik dashboard
    traefik-dashboard:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/traefik`)"
      service: api@internal
      entryPoints:
        - websecure
      middlewares:
        - traefik-auth
        - strip-prefix-traefik
      tls:
        certResolver: letsencrypt
      priority: 10

    prometheus:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/prometheus`)"
      service: prometheus@docker
      entryPoints:
        - websecure
      middlewares:
        - strip-prefix-prometheus
      tls:
        certResolver: letsencrypt
      priority: 20

    grafana-legacy-redirect:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/grafana`)"
      service: noop@internal
      entryPoints:
        - websecure
      middlewares:
        - redirect-from-grafana
      tls:
        certResolver: letsencrypt
      priority: 20

    alertmanager:
      rule: "Host(`${DOMAIN_NAME}`) && PathPrefix(`/alertmanager`)"
      service: alertmanager@docker
      entryPoints:
        - websecure
      middlewares:
        - strip-prefix-alertmanager
      tls:
        certResolver: letsencrypt
      priority: 20

    grafana-main:
      rule: "Host(`${DOMAIN_NAME}`)"
      service: grafana@docker
      entryPoints:
        - websecure
      middlewares:
        - secureHeaders
      tls:
        certResolver: letsencrypt
      priority: 1000
