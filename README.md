# Salmate Monitoring System

This repository contains the configuration and scripts for setting up a centralized monitoring system for Salmate services across multiple environments.

## Overview

The monitoring system consists of:

- **Traefik**: For routing, SSL termination, and dashboard access
- **Prometheus**: For metrics collection and storage
- **Grafana**: For visualization and dashboards
- **Blackbox Exporter**: For external HTTP and ICMP monitoring
- **Alertmanager**: For alert management and notifications (currently disabled)

The system is designed as a standalone solution that can be deployed on a fresh VM. It includes a central monitoring server that federates metrics from individual Prometheus instances running in different environments.

## Directory Structure

```
salmate-monitoring/
├── alertmanager/              # Alertmanager configuration
├── blackbox-exporter/         # Blackbox exporter configuration
├── docker-compose.yml         # Main Docker Compose file
├── docs/                      # Documentation and diagrams
│   └── diagrams/              # Architecture and workflow diagrams
├── grafana/                   # Grafana configuration
│   ├── config/
│   ├── dashboards/            # Grafana dashboard JSON files
│   └── provisioning/
│       ├── dashboards/
│       └── datasources/
├── prometheus/                # Prometheus configuration
│   └── rules/                 # Alert rules
├── scripts/                   # Management scripts
│   ├── cleanup.sh
│   └── restart.sh
└── traefik/                   # Traefik configuration
    ├── certs/                 # SSL certificates
    ├── config/                # Traefik configuration files
    │   └── dynamic/           # Dynamic configuration
    └── logs/                  # Traefik logs
```

## Setup Instructions

### Automated Deployment with GitHub Actions

The monitoring system can be automatically deployed using GitHub Actions whenever code is pushed to the main branch.

#### Prerequisites

1. **Target Server Requirements:**
   - Ubuntu/Debian server with SSH access
   - Docker and Docker Compose installed
   - Ports 80 and 443 open for Let's Encrypt SSL certificates
   - Domain name pointing to the server

2. **GitHub Repository Secrets:**
   Configure the following secrets in your GitHub repository (Settings → Secrets and variables → Actions):

   - `SSH_HOST`: Target deployment server hostname or IP address
   - `SSH_KEY`: Private SSH key for authentication (entire key content including headers)
   - `SSH_PORT`: SSH port number (usually 22)
   - `SSH_USER`: Username for SSH connection

#### Deployment Process

The GitHub Actions workflow automatically:

1. **Preserves existing data** during deployment:
   - Traefik SSL certificates (`traefik/cert/`)
   - Grafana dashboards and data (`grafana/data/`)
   - Prometheus metrics history (`prometheus/data/`)
   - Alertmanager data (`alertmanager/data/`)

2. **Deploys the application** to `~/salmate-monitoring` on the target server

3. **Validates the deployment** by checking:
   - Critical files exist (docker-compose.yml, configurations)
   - Docker and Docker Compose are available
   - Proper permissions are set

4. **Starts services** using `docker compose up -d`

5. **Performs health checks** to ensure all services are running

#### Manual Deployment Trigger

You can also trigger deployment manually:

1. Go to **Actions** tab in your GitHub repository
2. Select **Deploy Salmate Monitoring** workflow
3. Click **Run workflow**
4. Optionally check **Force deployment** to deploy even if no changes are detected


### Restart the Monitoring System

To restart the monitoring system:

```bash
./scripts/restart.sh
```

### Cleanup

To clean up the monitoring system:

```bash
./scripts/cleanup.sh
```

This will stop and remove all containers, volumes, and the progress file.

## Accessing the Monitoring System

Once deployed, the monitoring system is accessible at:

- **Traefik Dashboard**: `https://${DOMAIN_NAME}/traefik/`
- **Prometheus**: `https://${DOMAIN_NAME}/prometheus/`
- **Grafana**: `https://${DOMAIN_NAME}/` (now accessible at root domain)
- **Alertmanager**: `https://${DOMAIN_NAME}/alertmanager/`

Default traefik credentials:
- Username: admin
- Password: admin (as configured in routes.yaml)

Default Grafana credentials:
- Username: admin
- Password: admin (as configured in .env)

## Configuration

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Domain configuration
DOMAIN_NAME=your-domain.example.com

# Traefik configuration
TRAEFIK_HTTP_PORT=80
TRAEFIK_HTTPS_PORT=443
TRAEFIK_METRICS_PORT=8082
TRAEFIK_ACME_EMAIL=<EMAIL>
TRAEFIK_DASHBOARD_AUTH=admin:<hashed_secure_password_here>

# Prometheus configuration
PROMETHEUS_PORT=9090

# Grafana settings
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=<secure_password_here>
```

The `DOMAIN_NAME` variable is used to generate the URLs for all services using path-based routing, allowing you to use a single domain name without needing to configure subdomains.

## Monitoring Features

### Federation

The system is configured to federate metrics from multiple Prometheus instances running in different environments.

### External Monitoring

The Blackbox Exporter is configured to monitor the availability and response time of external websites.

### Dashboards

The system comes with pre-configured Grafana dashboards:

- **Salmate System Health**: Overview of system health metrics
- **View Containers**: Docker container metrics
- **View Nodes**: Node-level system metrics
- **View PostgreSQL**: PostgreSQL database metrics
- **View Redis**: Redis metrics
- **View Traefik Stats**: Monitoring of the Traefik reverse proxy

## Customization

### Adding New Alert Rules

Add new alert rules in the `prometheus/rules/` directory. For example:

```yaml
groups:
- name: custom_alerts
  rules:
  - alert: HighMemoryUsage
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage on {{ $labels.instance }}"
      description: "Memory usage is above 85% on {{ $labels.instance }} for more than 5 minutes."
```

### Adding New Dashboards

Add new Grafana dashboard JSON files to the `grafana/dashboards/` directory. The dashboards will be automatically loaded by Grafana through the provisioning configuration.
