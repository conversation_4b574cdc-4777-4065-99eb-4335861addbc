# Salmate Monitoring System Architecture

This diagram illustrates the architecture of the Salmate Monitoring System, showing all major components, data flows, federation relationships, and monitoring targets.

## Architecture Diagram

```mermaid
flowchart TB
    %% Define styles
    classDef container fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff
    classDef externalSystem fill:#f9f,stroke:#333,stroke-width:1px
    classDef network fill:#f96,stroke:#333,stroke-width:1px,color:#000
    classDef user fill:#9c6,stroke:#333,stroke-width:1px
    classDef dataStore fill:#fd8,stroke:#333,stroke-width:1px,color:#000
    
    %% External User
    User([External User]):::user
    
    %% Network boundary
    subgraph DockerNetwork["Docker Network: monitoring_network"]
        direction TB
        
        %% Traefik
        subgraph TraefikService["Traefik Service"]
            Traefik[("Traefik v3.0")]:::container
            TraefikConfig["Configuration 
                           /etc/traefik"]:::dataStore
            TraefikCerts["SSL Certificates 
                          /etc/traefik/certs"]:::dataStore
            
            Traefik --- TraefikConfig
            Traefik --- TraefikCerts
        end
        
        %% Prometheus
        subgraph PrometheusService["Prometheus Service"]
            Prometheus[("Prometheus Metrics Collection")]:::container
            PrometheusConfig["Configuration
                              /etc/prometheus"]:::dataStore
            PrometheusData["Time Series Data 
                            /prometheus"]:::dataStore
            
            Prometheus --- PrometheusConfig
            Prometheus --- PrometheusData
        end
        
        %% Grafana
        subgraph GrafanaService["Grafana Service"]
            Grafana[("Grafana Visualization")]:::container
            GrafanaConfig["Configuration 
                           /etc/grafana"]:::dataStore
            GrafanaDashboards["Dashboards 
                               /etc/grafana/dashboards"]:::dataStore
            GrafanaData["Grafana Data
                         /var/lib/grafana"]:::dataStore
            
            Grafana --- GrafanaConfig
            Grafana --- GrafanaDashboards
            Grafana --- GrafanaData
        end
        
        %% Blackbox Exporter 
        subgraph BlackboxService["Blackbox Exporter Service"]
            Blackbox[("Blackbox Exporter")]:::container
            BlackboxConfig["Configuration 
                            /etc/prometheus/blackbox-exporter.yml"]:::dataStore
            
            Blackbox --- BlackboxConfig
        end
        
        %% Alertmanager (disabled)
        subgraph AlertmanagerService["Alertmanager Service (Disabled)"]
            Alertmanager[("Alertmanager")]:::container
            style Alertmanager opacity:0.5,stroke-dasharray:5
            AlertmanagerConfig["Configuration 
                                /etc/alertmanager"]:::dataStore
            style AlertmanagerConfig opacity:0.5,stroke-dasharray:5
            
            Alertmanager --- AlertmanagerConfig
        end
    end
    
    %% External Prometheus instances
    subgraph ExternalEnvironments["External Environments"]
        direction TB
        
        subgraph StagingEnv["Staging Environment"]
            StagingProm["Prometheus 
                         prom.salmate-staging.aibrainlab.co"]:::externalSystem
            StagingWeb["Web Service
                        salmate-staging.aibrainlab.co"]:::externalSystem
        end
        
        subgraph TPBEnv["TPB Environment"]
            TPBProm["Prometheus 
                     prom.tpb.aibrainlab.co"]:::externalSystem
            TPBWeb["Web Service 
                    tpb.aibrainlab.co"]:::externalSystem
        end
        
        subgraph ViriyahEnv["Viriyah Environment"]
            ViriyahProm["Prometheus 
                         prom.viriyah.aibrainlab.co"]:::externalSystem
            ViriyahWeb["Web Service
                        viriyah.aibrainlab.co"]:::externalSystem
        end
        
        subgraph SilaEnv["Sila Dev Environment"]
            SilaProm["Prometheus 
                      prom.sila-dev.aibrainlab.co"]:::externalSystem
            SilaWeb["Web Service 
                     sila-dev.aibrainlab.co"]:::externalSystem
        end
    end
    
    %% Connections and data flows
    
    %% User access
    User -->|"HTTPS Port 443"| Traefik
    
    %% Internal connections
    Traefik -->|"Route: 
                 /prometheus 
                 Port 9090"| Prometheus
    Traefik -->|"Route: 
                 /grafana 
                 Port 3000"| Grafana
    Traefik -->|"Route: 
                 /alertmanager 
                 Port 9093 
                 (Disabled)"| Alertmanager
    
    %% Prometheus to Grafana
    Prometheus -->|"Data Source HTTP"| Grafana
    
    %% Prometheus to Blackbox
    Prometheus -->|"Scrape HTTP"| Blackbox
    
    %% Prometheus to Alertmanager
    Prometheus -.->|"Alerts (Disabled)"| Alertmanager
    
    %% Federation connections
    Prometheus -->|"Federation 
                    /federate"| StagingProm
    Prometheus -->|"Federation 
                    /federate"| TPBProm
    Prometheus -->|"Federation 
                    /federate"| ViriyahProm
    Prometheus -->|"Federation 
                    /federate"| SilaProm
    
    %% Blackbox monitoring
    Blackbox -->|"HTTP Probe"| StagingWeb
    Blackbox -->|"HTTP Probe"| TPBWeb
    Blackbox -->|"HTTP Probe"| ViriyahWeb
    Blackbox -->|"HTTP Probe"| SilaWeb
    
    %% Legend
    subgraph Legend
        Container["Docker Container"]:::container
        External["External System"]:::externalSystem
        Network["Network Boundary"]:::network
        UserIcon["User"]:::user
        DataStore["Data Store"]:::dataStore
    end
```

## Component Descriptions

### Core Components

- **Traefik**: Reverse proxy and load balancer that handles routing, SSL termination, and dashboard access
- **Prometheus**: Time-series database for metrics collection, storage, and querying
- **Grafana**: Visualization platform for metrics and dashboards
- **Blackbox Exporter**: Probes external HTTP endpoints and ICMP targets
- **Alertmanager**: Alert management and notification system (currently disabled)

### Data Flows

1. **User Access Flow**:
   - External users access the system through Traefik on HTTPS (port 443)
   - Traefik routes requests to the appropriate service based on path

2. **Metrics Collection Flow**:
   - Prometheus federates metrics from external Prometheus instances
   - Prometheus scrapes Blackbox Exporter for external endpoint monitoring
   - Prometheus provides metrics to Grafana for visualization

3. **Monitoring Flow**:
   - Blackbox Exporter probes external web services for availability and performance
   - Results are collected by Prometheus and visualized in Grafana

### External Environments

The system monitors four environments:
- **Staging**: salmate-staging.aibrainlab.co
- **TPB**: tpb.aibrainlab.co
- **Viriyah**: viriyah.aibrainlab.co
- **Sila Dev**: sila-dev.aibrainlab.co

Each environment has a Prometheus instance that is federated and a web service that is monitored via Blackbox Exporter.
