global:
  resolve_timeout: 1m
  slack_api_url: "*********************************************************************************"

route:
  group_by: [alertname]
  group_wait: 30s
  group_interval: 1m
  repeat_interval: 2h
  receiver: "team-email-and-slack"

receivers:
  - name: "team-email-and-slack"
    # email_configs:
    #   - to: "<EMAIL>"
    #     from: "<EMAIL>"
    #     smarthost: "smtp.gmail.com:587"
    #     auth_username: "<EMAIL>"
    #     auth_identity: "<EMAIL>"
    #     auth_password: "gcyhcfuhcvfvalgt"
    #     require_tls: true
    #     send_resolved: true

    #   - to: "<EMAIL>"
    #     from: "<EMAIL>"
    #     smarthost: "smtp.gmail.com:587"
    #     auth_username: "<EMAIL>"
    #     auth_identity: "<EMAIL>"
    #     auth_password: "gcyhcfuhcvfvalgt"
    #     require_tls: true
    #     send_resolved: true

    slack_configs:
      - channel: "#salmate-alert"
        send_resolved: true
        text: "Central Salmate Monitoring -- {{ .CommonAnnotations.description }}"
