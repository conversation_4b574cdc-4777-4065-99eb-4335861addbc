services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    command:
      - "--configFile=/etc/traefik/traefik.yaml"
      - "--providers.docker=true"
      - "--providers.docker.exposedByDefault=false"
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--providers.file.watch=true"
      - "--api.dashboard=true"
      - "--entrypoints.web.address=:${TRAEFIK_HTTP_PORT}"
      - "--entrypoints.websecure.address=:${TRAEFIK_HTTPS_PORT}"
      - "--entrypoints.metrics.address=:${TRAEFIK_METRICS_PORT}"
    ports:
      - "${TRAEFIK_HTTP_PORT}:${TRAEFIK_HTTP_PORT}"
      - "${TRAEFIK_HTTPS_PORT}:${TRAEFIK_HTTPS_PORT}"
      - "${TRAEFIK_METRICS_PORT}:${TRAEFIK_METRICS_PORT}"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./traefik/config/dynamic:/etc/traefik/dynamic:ro
      - ./traefik/cert:/etc/traefik/cert
      - ./traefik/logs:/var/log/traefik
    networks:
      - monitoring_network
    labels:
      - "traefik.enable=true"
      # Define the strip prefix middleware for other services
      - "traefik.http.middlewares.strip-prefix.stripprefix.prefixes=/prometheus,/alertmanager"
    env_file:
      - .env

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./prometheus:/etc/prometheus
      - ./prometheus/data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      # Removed trailing slash to prevent redirect loops
      - '--web.external-url=/prometheus'
      # Added prefix path to ensure proper path handling
      - '--web.route-prefix=/'
    ports:
      - "${PROMETHEUS_PORT}:${PROMETHEUS_PORT}"  # Expose port for direct access and debugging
    restart: unless-stopped
    networks:
      - monitoring_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`${DOMAIN_NAME}`) && PathPrefix(`/prometheus`)"
      - "traefik.http.routers.prometheus.service=prometheus"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"
      - "traefik.http.routers.prometheus.tls=true"
      - "traefik.http.routers.prometheus.entrypoints=websecure"
      - "traefik.http.routers.prometheus.tls.certresolver=letsencrypt"
      - "traefik.http.routers.prometheus.middlewares=strip-prefix@docker"
    env_file:
      - .env

# Alertmanager
  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    volumes:
      - ./alertmanager:/etc/alertmanager
      - ./alertmanager/data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    restart: unless-stopped
    networks:
      - monitoring_network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.alertmanager.rule=Host(`${DOMAIN_NAME}`) && PathPrefix(`/alertmanager`)"
      - "traefik.http.routers.alertmanager.service=alertmanager"
      - "traefik.http.services.alertmanager.loadbalancer.server.port=9093"
      - "traefik.http.routers.alertmanager.tls=true"
      - "traefik.http.routers.alertmanager.entrypoints=websecure"
      - "traefik.http.routers.alertmanager.tls.certresolver=letsencrypt"
      - "traefik.http.routers.alertmanager.middlewares=strip-prefix@docker"
    env_file:
      - .env

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    networks:
      - monitoring_network
    depends_on:
      - prometheus

    # Traefik configuration
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana-direct.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.grafana-direct.service=grafana"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"
      - "traefik.http.routers.grafana-direct.entrypoints=websecure"
      - "traefik.http.routers.grafana-direct.tls=true"
      - "traefik.http.routers.grafana-direct.tls.certresolver=letsencrypt"
      - "traefik.http.routers.grafana-direct.priority=1000"

    # Persistent volumes
    volumes:
      - ./grafana:/etc/grafana
      - ./grafana/dashboards:/etc/grafana/dashboards
      - ./grafana/data:/var/lib/grafana

    # Environment variables
    environment:
      # Path configurations
      - GF_PATHS_CONFIG=/etc/grafana/grafana.ini
      - GF_PATHS_DATA=/var/lib/grafana
      - GF_PATHS_LOGS=/var/log/grafana
      - GF_PATHS_PLUGINS=/var/lib/grafana/plugins
      - GF_PATHS_PROVISIONING=/etc/grafana/provisioning

      # Server settings
      - GF_SERVER_PROTOCOL=http
      - GF_SERVER_HTTP_PORT=3000
      - GF_SERVER_DOMAIN=${DOMAIN_NAME}
      - GF_SERVER_ROOT_URL=https://${DOMAIN_NAME}/
      - GF_SERVER_SERVE_FROM_SUB_PATH=false
      - GF_SERVER_ENFORCE_DOMAIN=false
      - GF_SERVER_COOKIE_SAMESITE=none
      - GF_SERVER_COOKIE_SECURE=true
      - GF_LOG_LEVEL=debug

      # Security settings
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_SECURITY_COOKIE_SECURE=true
      - GF_SECURITY_COOKIE_SAMESITE=none

      # Authentication
      - GF_AUTH_DISABLE_LOGIN_FORM=false
      - GF_AUTH_ANONYMOUS_ENABLED=false

      # Admin user (configured via environment variables)
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}

      # Logging (debug level for troubleshooting, can be reduced to 'info' in production)
      - GF_LOG_LEVEL=info
      - GF_LOG_MODE=console

      # Database (SQLite for simplicity, consider PostgreSQL for production)
      - GF_DATABASE_TYPE=sqlite3
      - GF_DATABASE_PATH=/var/lib/grafana/grafana.db

    # Environment file
    env_file:
      - .env

  blackbox-exporter:
    image: "prom/blackbox-exporter:latest"
    container_name: blackbox
    hostname: "blackbox"
    restart: always
    networks:
    - monitoring_network
    ports:
    - "9115:9115"
    volumes:
    - "./blackbox-exporter/blackbox-exporter.yml:/etc/prometheus/blackbox-exporter.yml"
    command:
    - "--config.file=/etc/prometheus/blackbox-exporter.yml"

networks:
  monitoring_network:
    name: monitoring_network
    driver: bridge